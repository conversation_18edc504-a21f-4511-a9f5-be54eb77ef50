/**
 * DYNAMIC FORM COMPONENT
 * 
 * This is the main dynamic form component that handles:
 * - Dynamic form generation based on metadata
 * - Multi-field support (add/remove fields dynamically)
 * - Grouped fields with nested groups
 * - Form validation and submission
 * - View/Edit modes
 * - Row view and nested view modes
 * 
 * KEY FEATURES:
 * - Supports both table-based and screen-based forms
 * - <PERSON>les complex nested group structures
 * - Manages form state and validation
 * - Integrates with metadata service for field definitions
 * - Supports multi-column layouts
 */

import { Component, Input, OnInit, OnDestroy, Output, EventEmitter, ViewChild, ElementRef, AfterViewInit, inject } from '@angular/core';
import { FormBuilder, FormGroup, FormArray, Validators, FormControl, AbstractControl } from "@angular/forms";
import { MetadataService } from '../services/metadata.service'
import { HttpClient } from "@angular/common/http";
import { CommonModule } from "@angular/common";
import { ReactiveFormsModule } from "@angular/forms";
import { environment } from '../../environments/environment';
import { TableUtilsService } from '../services/table-utils.service';

// Component imports - Child components for modular form rendering
import { InitialInputComponent } from './components/initial-input/initial-input.component';
import { FormHeaderComponent } from './components/form-header/form-header.component';
import { RegularFieldComponent } from './components/regular-field/regular-field.component';
import { FormActionsComponent } from './components/form-actions/form-actions.component';

// Angular Material imports - UI components for form elements
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatTableModule } from '@angular/material/table';
import { MatChipsModule } from '@angular/material/chips';
import { MatAutocompleteModule } from '@angular/material/autocomplete';

import { ChangeDetectorRef } from '@angular/core';

@Component({
  selector: 'app-dynamic-form',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    InitialInputComponent,
    FormHeaderComponent,
    RegularFieldComponent,
    FormActionsComponent,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatCheckboxModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatTooltipModule,
    MatExpansionModule,
    MatTableModule,
    MatChipsModule,
    MatAutocompleteModule
  ],
  templateUrl: './dynamic-form.component.html',
  styleUrl: './dynamic-form.component.scss'
})
export class DynamicFormComponent implements OnInit, OnDestroy, AfterViewInit {
  
  // ========================================
  // LAYOUT AND COLUMN MANAGEMENT
  // ========================================
  /** Number of columns for form layout (1, 2, 3, etc.) */
  columnCount = 1;
  /** Array of columns, each containing fields for that column */
  columns: any[][] = [];

  // ========================================
  // SUBSCREEN TAB MANAGEMENT
  // ========================================
  /** Flag indicating if the form has SubScreens */
  hasSubScreens = false;
  /** Array of SubScreen configurations */
  subScreens: any[] = [];
  /** Currently selected tab index */
  selectedTabIndex = 0;
  /** Array of columns for each SubScreen tab */
  subScreenColumns: { [key: string]: any[][] } = {};
  /** Form groups for each SubScreen */
  subScreenForms: { [key: string]: FormGroup } = {};
  /** Store the last metadata response for SubScreen processing */
  private lastMetadataResponse: any = null;

  // ========================================
  // INPUT PROPERTIES (from parent component)
  // ========================================
  /** Table name for metadata lookup - used for table-based forms */
  @Input() tableName!: string;
  /** Screen name for metadata lookup - used for screen-based forms */
  @Input() screenName!: string;
  /** Initial data to populate the form */
  @Input() data: any;
  /** Output event when form data changes */
  @Output() dataChange = new EventEmitter<any>();

  // ========================================
  // VIEW CHILDREN (references to child components)
  // ========================================
  @ViewChild('mySelect') mySelect!: ElementRef;
  @ViewChild('formActions') formActions!: FormActionsComponent;

  // ========================================
  // FORM STATE MANAGEMENT
  // ========================================
  /** Main form group that contains all form controls */
  form!: FormGroup;
  /** Array of field definitions from metadata */
  fields: any[] = [];
  /** Flag indicating if form submission was successful */
  submissionSuccess = false;
  /** Error message to display to user */
  errorMessage = "";
  /** Loading state for async operations */
  isLoading = false;
  /** Controls whether to show initial ID input or the full form */
  showInitialInput = true;
  /** Toggle between view mode (readonly) and edit mode */
  isViewMode = false;
  /** Authorization flag for form actions */
  isAuth = true;
  /** Success message to display after operations */
  successMessage: string = "";
  /** Flag to show success popup */
  showSuccessPopup = false;
  /** Flag to show validation messages */
  showValidation: boolean = false;
  /** Validation result object */
  validationResult: any;
  /** Flag indicating if form is tenant-based */
  isTenantBasedFlag: boolean = false;
  /** Authorization number for form actions */
  authorizeNumber = 1;
  /** Toggle between nested view and row view for grouped fields */
  isRowView: boolean = false;

  // ========================================
  // INJECTED SERVICES
  // ========================================
  private metadataService = inject(MetadataService);
  private fb = inject(FormBuilder);
  private http = inject(HttpClient);
  private tableUtilsService = inject(TableUtilsService);
  private cdRef = inject(ChangeDetectorRef);

  constructor() { }

  /**
   * LIFECYCLE: ngOnInit
   * Initializes the form when component loads
   * Checks for tableName or screenName and initializes form accordingly
   */
  ngOnInit() {
    if (this.tableName || this.screenName) {
      this.initializeForm();
    }
  }

  /**
   * LIFECYCLE: ngOnDestroy
   * Cleanup handled by individual components
   */
  ngOnDestroy() {
    // Cleanup handled by individual components
  }

  /**
   * LIFECYCLE: ngAfterViewInit
   * Post-view initialization - dropdown functionality handled by DropdownComponent
   */
  ngAfterViewInit() {
    // Dropdown functionality is now handled by the unified DropdownComponent
  }

  /**
   * INITIALIZATION: Creates the initial form structure
   * Sets up the base form with ID field as required
   */
  initializeForm() {
    this.form = this.fb.group({
      ID: ["", Validators.required],
    });
  }

  /**
   * FORM STATE: Sets the entire form to readonly or editable
   * Recursively disables/enables all form controls including nested groups and multi-fields
   * 
   * @param isReadonly - true to disable form, false to enable
   */
  setFormReadonly(isReadonly: boolean) {
    const disableControls = (control: AbstractControl) => {
      if (control instanceof FormGroup) {
        Object.values(control.controls).forEach(disableControls);
      } else if (control instanceof FormArray) {
        control.controls.forEach(disableControls);
      } else {
        if (isReadonly) {
          control.disable({ emitEvent: false });
        } else {
          control.enable({ emitEvent: false });
        }
      }
    };

    disableControls(this.form); // Apply disable logic to the entire form

    // Explicitly handle isMulti fields
    this.fields.forEach((field) => {
      if (field.isMulti) {
        const formArray = this.form.get(field.fieldName) as FormArray;
        if (formArray) {
          formArray.controls.forEach((control) => {
            if (isReadonly) {
              control.disable({ emitEvent: false });
            } else {
              control.enable({ emitEvent: false });
            }
          });
        }
      }

      // Explicitly handle grouped fields
      if (field.Group) {
        const groupArray = this.getGroupArray(field.Group);
        if (groupArray) {
          groupArray.controls.forEach((control) => {
            if (isReadonly) {
              control.disable({ emitEvent: false });
            } else {
              control.enable({ emitEvent: false });
            }
          });
        }
      }
    });

    // Handle SubScreen forms
    if (this.hasSubScreens) {
      this.subScreens.forEach((subScreenId: string) => {
        const subScreenForm = this.subScreenForms[subScreenId];
        if (subScreenForm) {
          disableControls(subScreenForm);
        }
      });
    }
  }

  /**
   * VIEW MODE: Switches to view mode and loads data
   * Validates ID field and loads data in readonly mode
   */
  viewData() {
    const idValue = this.form.get('ID')?.value;
    if (!idValue || idValue.trim() === '') {
      this.showValidation = true;
      return;
    }

    this.isViewMode = true;
    this.loadDataAndBuildForm();
    setTimeout(() => {
      this.setFormReadonly(true);
    }, 0);
  }

  /**
   * DATA LOADING: Loads data and builds the form structure
   * Validates ID, fetches data from API, and builds form with metadata
   */
  loadDataAndBuildForm() {
    const idValue = this.form.get('ID')?.value;
    if (!idValue || idValue.trim() === '') {
      this.showValidation = true;
      return;
    }

    this.isLoading = true;
    this.errorMessage = "";
    this.successMessage = "";
    const id = this.form.get("ID")?.value;
    const tableNameToUse = this.tableName || this.screenName;
    const tableNameForValidation = this.tableUtilsService.extractTablesApiId(tableNameToUse);
    const apiUrl = `${environment.baseURL}/api/validation/validate-id?tableName=${tableNameForValidation}&id=${id}`;

    this.http.get(apiUrl, { withCredentials: true }).subscribe({
      next: (response: any) => {
        if (response.success) {
          this.showInitialInput = false;
          this.loadTableMetadata();
        } else {
          this.errorMessage = response.message || "ID validation failed";
          this.isLoading = false;
        }
      },
      error: (error) => {
        this.errorMessage = "Error validating ID";
        this.isLoading = false;
      },
      complete: () => this.isLoading = false
    });
  }

  /**
   * METADATA LOADING: Fetches and processes metadata to build the form
   * Retrieves field definitions and organizes them into columns
   */
  loadTableMetadata() {
    this.isLoading = true;
    const metadataObservable = this.screenName
      ? this.metadataService.getScreenMetadata(this.screenName)
      : this.metadataService.getTableMetadata(this.tableName);

    metadataObservable.subscribe({
      next: (response: any) => {
        if (response?.data?.fieldName) {
          // Store the metadata response for SubScreen processing
          this.lastMetadataResponse = response.data;

          // ✅ Get columnNumber or default to 1
          this.columnCount = response.data.columnNumber || 1;

          // ✅ Order fields as usual
          const orderedFields = this.orderFieldsBasedOnFormDefinition(response.data.fieldName);
          // ⛔️ Skip "ID" so it doesn't take a layout slot
          const visibleFields = orderedFields.filter(
            field => field.fieldName?.toUpperCase() !== 'ID'
          );
          // ✅ Store isTenantBased if present
          if (response.data.isTenantBased) {
            this.isTenantBasedFlag = response.data.isTenantBased;
          }

          // ✅ Split fields into columns
          this.columns = this.distributeFieldsRoundRobin(visibleFields, this.columnCount);

          // ✅ Still keep old field assignment if you rely on it elsewhere
          this.fields = orderedFields;

          // ========================================
          // SUBSCREEN PROCESSING
          // ========================================
          this.processSubScreens(response.data);

          this.buildForm();
          this.fetchFormData();
          // Handle defaultFields if present in metadata response
          if (response.data.defaultFields && Array.isArray(response.data.defaultFields)) {
            this.populateDefaultFields(response.data.defaultFields);
          }
        } else {
          this.isLoading = false;
        }
      },
      error: (error) => {
        this.isLoading = false;
      },
      complete: () => (this.isLoading = false),
    });
  }

  /**
   * SUBSCREEN PROCESSING: Processes SubScreen metadata and sets up tab structure
   * @param data - The metadata response data
   */
  private processSubScreens(data: any) {
    // Check if SubScreens exist in the response
    if (data.subScreen && Array.isArray(data.subScreen) && data.subScreen.length > 0) {
      this.hasSubScreens = true;
      this.subScreens = data.subScreen;
      
      // Process each SubScreen
      this.subScreens.forEach((subScreenId: string) => {
        // Find the corresponding metadata for this SubScreen
        const subScreenMetadata = this.findSubScreenMetadata(data, subScreenId);
        if (subScreenMetadata) {
          this.processSubScreenMetadata(subScreenId, subScreenMetadata);
        }
      });
    } else {
      this.hasSubScreens = false;
      this.subScreens = [];
    }
  }

  /**
   * Finds the metadata for a specific SubScreen
   * @param data - The main metadata response
   * @param subScreenId - The SubScreen ID to find
   * @returns The SubScreen metadata or null if not found
   */
  private findSubScreenMetadata(data: any, subScreenId: string): any {
    if (data.subScreensMetadata && Array.isArray(data.subScreensMetadata)) {
      return data.subScreensMetadata.find((subScreen: any) => subScreen.ID === subScreenId);
    }
    return null;
  }

  /**
   * Processes metadata for a specific SubScreen
   * @param subScreenId - The SubScreen ID
   * @param subScreenMetadata - The SubScreen metadata
   */
  private processSubScreenMetadata(subScreenId: string, subScreenMetadata: any) {
    // Get column count for this SubScreen
    const columnCount = subScreenMetadata.columnNumber || 1;
    
    // Order fields for this SubScreen
    const orderedFields = this.orderFieldsBasedOnFormDefinition(subScreenMetadata.fieldName || []);
    
    // Filter out ID field
    const visibleFields = orderedFields.filter(
      field => field.fieldName?.toUpperCase() !== 'ID'
    );
    
    // Distribute fields into columns for this SubScreen
    this.subScreenColumns[subScreenId] = this.distributeFieldsRoundRobin(visibleFields, columnCount);
    
    // Create form group for this SubScreen
    this.subScreenForms[subScreenId] = this.createSubScreenForm(subScreenMetadata);
  }

  /**
   * Creates a FormGroup for a SubScreen with support for groups and multi-fields
   * @param subScreenMetadata - The SubScreen metadata
   * @returns FormGroup for the SubScreen
   */
  private createSubScreenForm(subScreenMetadata: any): FormGroup {
    const formGroup = this.fb.group({});
    const groupedFields: { [key: string]: FormArray } = {};

    if (subScreenMetadata.fieldName && Array.isArray(subScreenMetadata.fieldName)) {
      // First, create all parent groups for this SubScreen
      const parentGroups = this.getSubScreenParentGroups(subScreenMetadata.fieldName);
      parentGroups.forEach(parentGroup => {
        if (!groupedFields[parentGroup]) {
          groupedFields[parentGroup] = this.fb.array([]);
          formGroup.addControl(parentGroup, groupedFields[parentGroup]);
          this.addSubScreenGroup(parentGroup, formGroup);
        }
      });

      // Process each field
      subScreenMetadata.fieldName.forEach((field: any) => {
        if (field.fieldName !== "ID") {
          if (field.isMulti && !field.Group) {
            // Non-grouped multi-field
            const multiFieldArray = this.fb.array([this.createMultiField(field)]);
            formGroup.addControl(field.fieldName, multiFieldArray);

            // Disable multi-field if noInput is true
            if (field.noInput) {
              multiFieldArray.disable({ emitEvent: false });
            }
          } else if (field.Group) {
            const parsed = this.parseGroupPath(field.Group);
            if (!parsed.isNested) {
              // Direct field of parent group - already handled in addSubScreenGroup
            } else {
              // Nested group field - already handled in addSubScreenGroup
            }
          } else {
            // Non-grouped regular field
            const validators = field.mandatory ? Validators.required : null;
            let control;
            
            switch (field.type) {
              case "boolean":
                control = this.fb.control(false, validators);
                break;
              case "date":
                control = this.fb.control(null, validators);
                break;
              default:
                control = this.fb.control("", validators);
                break;
            }
            
            formGroup.addControl(field.fieldName, control);
            
            // Disable control if noInput is true
            if (field.noInput) {
              control.disable({ emitEvent: false });
            }
          }
        }
      });
    }
    
    return formGroup;
  }

  /**
   * Gets parent groups for a SubScreen's fields
   * @param fields - Array of field definitions for the SubScreen
   * @returns Array of parent group names
   */
  private getSubScreenParentGroups(fields: any[]): string[] {
    const parentGroups = new Set<string>();
    
    fields.forEach(field => {
      if (field.Group) {
        const parsed = this.parseGroupPath(field.Group);
        if (parsed.parent && !parsed.isNested) {
          parentGroups.add(parsed.parent);
        }
      }
    });
    
    return Array.from(parentGroups);
  }

  /**
   * Adds a group to a SubScreen form
   * @param groupName - The name of the group to add
   * @param formGroup - The SubScreen form group
   */
  private addSubScreenGroup(groupName: string, formGroup: FormGroup) {
    const groupArray = formGroup.get(groupName) as FormArray;
    if (groupArray) {
      const group = this.createSubScreenGroup(groupName);
      groupArray.push(group);
    }
  }

  /**
   * Creates a FormGroup for a SubScreen group
   * @param groupName - The name of the group
   * @returns FormGroup for the SubScreen group
   */
  private createSubScreenGroup(groupName: string): FormGroup {
    const group = this.fb.group({});

    // Handle nested groups
    const parsed = this.parseGroupPath(groupName);
    if (parsed.isNested && parsed.parent && parsed.child) {
      // This is a nested group, create fields for this specific path
      this.getSubScreenFieldsForGroupPath(groupName).forEach((field) => {
        this.addSubScreenFieldToGroup(group, field);
      });
    } else {
      // This is a parent group, create fields and nested subgroups
      this.getSubScreenFieldsForGroup(groupName).forEach((field) => {
        const fieldParsed = this.parseGroupPath(field.Group);
        if (!fieldParsed.isNested) {
          // Direct field of this group
          this.addSubScreenFieldToGroup(group, field);
        }
      });

      // Add nested subgroups
      const childGroups = this.getSubScreenChildGroups(groupName);
      childGroups.forEach(childGroup => {
        const childGroupArray = this.fb.array([this.createSubScreenGroup(`${groupName}|${childGroup}`)]);
        group.addControl(childGroup, childGroupArray);
      });
    }

    return group;
  }

  /**
   * Gets fields for a specific group in SubScreen
   * @param groupName - The name of the group
   * @param subScreenId - The SubScreen ID (optional, uses selected if not provided)
   * @returns Array of fields for the group
   */
  getSubScreenFieldsForGroup(groupName: string, subScreenId?: string): any[] {
    const targetId = subScreenId || this.getSelectedSubScreenId();
    if (!targetId || !this.lastMetadataResponse) return [];
    
    // Get the SubScreen metadata
    const subScreenMetadata = this.findSubScreenMetadata(this.lastMetadataResponse, targetId);
    if (!subScreenMetadata || !subScreenMetadata.fieldName) return [];
    
    return subScreenMetadata.fieldName.filter((field: any) => field.Group === groupName);
  }

  /**
   * Gets fields for a specific group path in SubScreen
   * @param groupPath - The group path
   * @param subScreenId - The SubScreen ID (optional, uses selected if not provided)
   * @returns Array of fields for the group path
   */
  getSubScreenFieldsForGroupPath(groupPath: string, subScreenId?: string): any[] {
    const targetId = subScreenId || this.getSelectedSubScreenId();
    if (!targetId || !this.lastMetadataResponse) return [];
    
    // Get the SubScreen metadata
    const subScreenMetadata = this.findSubScreenMetadata(this.lastMetadataResponse, targetId);
    if (!subScreenMetadata || !subScreenMetadata.fieldName) return [];
    
    return subScreenMetadata.fieldName.filter((field: any) => field.Group === groupPath);
  }

  /**
   * Gets child groups for a parent group in SubScreen
   * @param parentGroup - The parent group name
   * @param subScreenId - The SubScreen ID (optional, uses selected if not provided)
   * @returns Array of child group names
   */
  getSubScreenChildGroups(parentGroup: string, subScreenId?: string): string[] {
    const targetId = subScreenId || this.getSelectedSubScreenId();
    if (!targetId || !this.lastMetadataResponse) return [];
    
    // Get the SubScreen metadata
    const subScreenMetadata = this.findSubScreenMetadata(this.lastMetadataResponse, targetId);
    if (!subScreenMetadata || !subScreenMetadata.fieldName) return [];
    
    const childGroups = new Set<string>();
    
    subScreenMetadata.fieldName.forEach((field: any) => {
      if (field.Group) {
        const parsed = this.parseGroupPath(field.Group);
        if (parsed.parent === parentGroup && parsed.child) {
          childGroups.add(parsed.child);
        }
      }
    });
    
    return Array.from(childGroups);
  }

  /**
   * Helper method to add a field to a SubScreen FormGroup
   * @param group - The FormGroup to add the field to
   * @param field - The field to add
   */
  private addSubScreenFieldToGroup(group: FormGroup, field: any): void {
    if (field.isMulti) {
      const multiFieldArray = this.fb.array([this.createMultiField(field)]);
      group.addControl(field.fieldName, multiFieldArray);

      // Disable multi-field if noInput is true
      if (field.noInput) {
        multiFieldArray.disable({ emitEvent: false });
      }
    } else if (field.foreginKey) {
      const control = this.fb.control("", field.mandatory ? Validators.required : null);
      group.addControl(field.fieldName, control);

      // Disable control if noInput is true
      if (field.noInput) {
        control.disable({ emitEvent: false });
      }
    } else {
      const control = this.fb.control("", field.mandatory ? Validators.required : null);
      group.addControl(field.fieldName, control);

      // Disable control if noInput is true
      if (field.noInput) {
        control.disable({ emitEvent: false });
      }
    }
  }

  /**
   * TAB MANAGEMENT: Changes the selected tab
   * @param index - The index of the tab to select
   */
  selectTab(index: number) {
    this.selectedTabIndex = index;
  }

  /**
   * Gets the currently selected SubScreen ID
   * @returns The SubScreen ID of the selected tab
   */
  getSelectedSubScreenId(): string {
    if (this.hasSubScreens && this.subScreens.length > 0) {
      return this.subScreens[this.selectedTabIndex];
    }
    return '';
  }

  /**
   * Gets the form group for the currently selected SubScreen
   * @returns FormGroup for the selected SubScreen
   */
  getSelectedSubScreenForm(): FormGroup {
    const selectedId = this.getSelectedSubScreenId();
    return selectedId ? this.subScreenForms[selectedId] || this.form : this.form;
  }

  /**
   * Gets the columns for the currently selected SubScreen
   * @returns Array of columns for the selected SubScreen
   */
  getSelectedSubScreenColumns(): any[][] {
    const selectedId = this.getSelectedSubScreenId();
    return selectedId ? this.subScreenColumns[selectedId] || [] : [];
  }

  /**
   * Gets the column count for the currently selected SubScreen
   * @returns Number of columns for the selected SubScreen
   */
  getSelectedSubScreenColumnCount(): number {
    const selectedColumns = this.getSelectedSubScreenColumns();
    return selectedColumns.length;
  }

  /**
   * Gets the columns for a specific SubScreen
   * @param subScreenId - The SubScreen ID
   * @returns Array of columns for the specified SubScreen
   */
  getSubScreenColumns(subScreenId: string): any[][] {
    return this.subScreenColumns[subScreenId] || [];
  }

  /**
   * Gets the column count for a specific SubScreen
   * @param subScreenId - The SubScreen ID
   * @returns Number of columns for the specified SubScreen
   */
  getSubScreenColumnCount(subScreenId: string): number {
    const columns = this.getSubScreenColumns(subScreenId);
    return columns.length;
  }

  /**
   * Gets the form group for a specific SubScreen
   * @param subScreenId - The SubScreen ID
   * @returns FormGroup for the specified SubScreen
   */
  getSubScreenForm(subScreenId: string): FormGroup {
    return this.subScreenForms[subScreenId] || this.form;
  }

  /**
   * LAYOUT: Distributes fields across columns using round-robin algorithm
   * Creates equal distribution of fields across the specified number of columns
   * 
   * @param fields - Array of field definitions
   * @param columnCount - Number of columns to distribute fields across
   * @returns Array of columns, each containing fields for that column
   */
  private distributeFieldsRoundRobin(fields: any[], columnCount: number): any[][] {
    const columns: any[][] = Array.from({ length: columnCount }, () => []);
    fields.forEach((field, index) => {
      const colIndex = index % columnCount;
      columns[colIndex].push(field);
    });
    return columns;
  }

  /**
   * FIELD ORDERING: Orders fields based on the formDefinition response structure
   * Fields with Group "fieldName" appear first, then other fields in their original order
   * 
   * @param fields - Array of field definitions to order
   * @returns Ordered array of fields
   */
  private orderFieldsBasedOnFormDefinition(fields: any[]): any[] {
    if (!fields || !Array.isArray(fields)) {
      return fields;
    }

    // Separate fields with Group "fieldName" and preserve their order
    const fieldNameGroupFields = fields.filter(field => field.Group === "fieldName");

    // Get all other fields (non-fieldName group) and preserve their order
    const otherFields = fields.filter(field => field.Group !== "fieldName");

    // Combine: fieldName group fields first, then other fields in their original order
    return [
      ...fieldNameGroupFields,
      ...otherFields
    ];
  }

  /**
   * FORM BUILDING: Constructs the main form group and its nested groups/fields
   * Creates FormGroups for all fields and their respective controls
   */
  buildForm() {
    const groupedFields: { [key: string]: FormArray } = {};

    // First, create all parent groups
    const parentGroups = this.getParentGroups();
    parentGroups.forEach(parentGroup => {
      if (!groupedFields[parentGroup]) {
        groupedFields[parentGroup] = this.fb.array([]);
        this.form.addControl(parentGroup, groupedFields[parentGroup]);
        this.addGroup(parentGroup);
      }
    });

    this.fields.forEach((field) => {
      if (field.fieldName !== "ID") {
        if (field.isMulti && !field.Group) {
          // Non-grouped multi-field
          const multiFieldArray = this.fb.array([this.createMultiField(field)]);
          this.form.addControl(field.fieldName, multiFieldArray);

          // Disable multi-field if noInput is true
          if (field.noInput) {
            multiFieldArray.disable({ emitEvent: false });
          }
        } else if (field.Group) {
          const parsed = this.parseGroupPath(field.Group);
          if (!parsed.isNested) {
            // Direct field of parent group - already handled in createGroup
          } else {
            // Nested group field - already handled in createGroup
          }
        } else {
          // Non-grouped regular field
          const validators = field.mandatory ? Validators.required : null;
          let control;
          switch (field.type) {
            case "boolean":
              control = this.fb.control(false, validators);
              break;
            case "date":
              control = this.fb.control(null, validators);
              break;
            default:
              control = this.fb.control("", validators);
              break;
          }
          this.form.addControl(field.fieldName, control);

          // Disable control if noInput is true
          if (field.noInput) {
            control.disable({ emitEvent: false });
          }
        }
      }
    });

    // Add SubScreen forms to the main form
    if (this.hasSubScreens) {
      this.subScreens.forEach((subScreenId: string) => {
        if (this.subScreenForms[subScreenId]) {
          this.form.addControl(subScreenId, this.subScreenForms[subScreenId]);
        }
      });
    }
  }




  /**
   * GROUP CREATION: Creates a FormGroup for a specific group name
   * Handles both parent and nested group creation
   */
  createGroup(groupName: string): FormGroup {
    const group = this.fb.group({});

    // Handle nested groups
    const parsed = this.parseGroupPath(groupName);
    if (parsed.isNested && parsed.parent && parsed.child) {
      // This is a nested group, create fields for this specific path
      this.getFieldsForGroupPath(groupName).forEach((field) => {
        this.addFieldToGroup(group, field);
      });
    } else {
      // This is a parent group, create fields and nested subgroups
      this.getFieldsForGroup(groupName).forEach((field) => {
        const fieldParsed = this.parseGroupPath(field.Group);
        if (!fieldParsed.isNested) {
          // Direct field of this group
          this.addFieldToGroup(group, field);
        }
      });

      // Add nested subgroups
      const childGroups = this.getChildGroups(groupName);
      childGroups.forEach(childGroup => {
        const childGroupArray = this.fb.array([this.createGroup(`${groupName}|${childGroup}`)]);
        group.addControl(childGroup, childGroupArray);
      });
    }

    return group;
  }

  /**
   * Helper method to add a field to a FormGroup
   */
  private addFieldToGroup(group: FormGroup, field: any): void {
    if (field.isMulti) {
      const multiFieldArray = this.fb.array([this.createMultiField(field)]);
      group.addControl(field.fieldName, multiFieldArray);

      // Disable multi-field if noInput is true
      if (field.noInput) {
        multiFieldArray.disable({ emitEvent: false });
      }
    } else if (field.foreginKey) {
      const control = this.fb.control("", field.mandatory ? Validators.required : null);
      group.addControl(field.fieldName, control);

      // Disable control if noInput is true
      if (field.noInput) {
        control.disable({ emitEvent: false });
      }


    } else {
      const control = this.fb.control("", field.mandatory ? Validators.required : null);
      group.addControl(field.fieldName, control);

      // Disable control if noInput is true
      if (field.noInput) {
        control.disable({ emitEvent: false });
      }
    }
  }

  // ========================================
  // MULTI FIELD COMPONENT SECTION
  // ========================================

  /**
   * Creates a FormGroup for multi-field components
   * @param field - Field configuration object
   * @returns FormGroup for the multi-field
   */
  createMultiField(field: any): FormGroup {
    const group = this.fb.group({});
    if (Array.isArray(field)) {
      field.forEach(fieldName => {
        const control = this.fb.control('', Validators.required);
        group.addControl(fieldName, control);
      });
    } else {
      const control = this.fb.control("", field.mandatory ? Validators.required : null);
      group.addControl(field.fieldName, control);

      // Disable control if noInput is true
      if (field.noInput) {
        control.disable({ emitEvent: false });
      }
    }
    return group;
  }

  /**
   * Gets the FormArray for a multi-field
   * @param fieldName - Name of the field
   * @param groupIndex - Index of the group (optional)
   * @param groupName - Name of the group (optional)
   * @param nestedGroupIndex - Index of nested group (optional)
   * @returns FormArray for the multi-field
   */
  getMultiArray(fieldName: string, groupIndex?: number, groupName?: string, nestedGroupIndex?: number): FormArray {
    if (groupIndex !== undefined && groupName) {
      // Check if this is a nested group
      const parsed = this.parseGroupPath(groupName);
      if (parsed.isNested && parsed.parent && parsed.child && nestedGroupIndex !== undefined) {
        // Navigate to nested group: parent[groupIndex].child[nestedGroupIndex].fieldName
        const parentArray = this.getGroupArray(parsed.parent);
        const parentGroup = parentArray.at(groupIndex) as FormGroup;
        const childArray = parentGroup.get(parsed.child) as FormArray;
        const childGroup = childArray.at(nestedGroupIndex) as FormGroup;
        return childGroup.get(fieldName) as FormArray;
      } else {
        // Regular group
        const groupArray = this.getGroupArray(groupName);
        const group = groupArray.at(groupIndex) as FormGroup;
        return group.get(fieldName) as FormArray;
      }
    } else {
      return this.form.get(fieldName) as FormArray;
    }
  }

  /**
   * Adds a new multi-field instance
   * @param field - Field configuration
   * @param groupIndex - Index of the group (optional)
   * @param index - Index to insert at (optional)
   * @param groupName - Name of the group (optional)
   * @param nestedGroupIndex - Index of nested group (optional)
   */
  addMultiField(field: any, groupIndex?: number, index?: number, groupName?: string, nestedGroupIndex?: number) {
    try {
      const multiArray = this.getMultiArray(field.fieldName, groupIndex, groupName, nestedGroupIndex);
      const newField = this.createMultiField(field);
      if (index !== undefined) {
        multiArray.insert(index + 1, newField);
      } else {
        multiArray.push(newField);
      }

    } catch (error) {
      // Handle error silently
    }
  }

  /**
   * Removes a multi-field instance
   * @param fieldName - Name of the field
   * @param index - Index of the field to remove
   * @param groupIndex - Index of the group (optional)
   * @param groupName - Name of the group (optional)
   * @param nestedGroupIndex - Index of nested group (optional)
   */
  removeMultiField(fieldName: string, index: number, groupIndex?: number, groupName?: string, nestedGroupIndex?: number) {
    const multiArray = this.getMultiArray(fieldName, groupIndex, groupName, nestedGroupIndex);
    multiArray.removeAt(index);
  }

  /**
   * Creates a FormGroup for multi-field with metadata (used for data population)
   * @param fieldMeta - Field metadata
   * @param sampleData - Sample data for population (optional)
   * @returns FormGroup for the multi-field
   */
  createMultiField2(fieldMeta: any, sampleData?: any): FormGroup {
    const group = this.fb.group({});
    const control = this.fb.control(sampleData || "", fieldMeta.mandatory ? Validators.required : null);
    group.addControl(fieldMeta.fieldName, control);
    return group;
  }

  // ========================================
  // GROUPED FIELDS COMPONENT SECTION
  // ========================================

  /**
   * Gets all fields that belong to a specific group
   * @param groupName - Name of the group
   * @returns Array of fields belonging to this group
   */
  getFieldsForGroup(groupName: string) {
    return this.fields.filter((field) => field.Group && field.Group.trim() === groupName.trim());
  }

  /**
   * Get fields for a specific group path (supports nested groups with pipe notation)
   * @param groupPath - The full group path (e.g., "type|field")
   * @returns Array of fields belonging to this group path
   */
  getFieldsForGroupPath(groupPath: string) {
    return this.fields.filter((field) => field.Group && field.Group.trim() === groupPath.trim());
  }

  /**
   * Parse group path to get parent and child group names
   * @param groupPath - The group path (e.g., "type|field")
   * @returns Object with parent and child group names
   */
  parseGroupPath(groupPath: string): { parent: string | null, child: string | null, isNested: boolean } {
    // Trim the entire groupPath first to handle trailing spaces
    const trimmedGroupPath = groupPath.trim();

    if (trimmedGroupPath.includes('|')) {
      const parts = trimmedGroupPath.split('|');
      return {
        parent: parts[0].trim(),
        child: parts[1].trim(),
        isNested: true
      };
    }
    return {
      parent: trimmedGroupPath.trim(),
      child: null,
      isNested: false
    };
  }

  /**
   * Get all unique parent groups
   */
  getParentGroups(): string[] {
    const parentGroups = new Set<string>();
    this.fields.forEach(field => {
      if (field.Group) {
        const parsed = this.parseGroupPath(field.Group);
        if (parsed.parent) {
          parentGroups.add(parsed.parent);
        }
      }
    });
    return Array.from(parentGroups);
  }

  /**
   * Get all child groups for a specific parent group
   */
  getChildGroups(parentGroup: string): string[] {
    const childGroups = new Set<string>();
    this.fields.forEach(field => {
      if (field.Group) {
        const parsed = this.parseGroupPath(field.Group);
        if (parsed.parent === parentGroup.trim() && parsed.child) {
          childGroups.add(parsed.child);
        }
      }
    });
    return Array.from(childGroups);
  }

  getGroupArray(groupName: string): FormArray {
    return this.form.get(groupName) as FormArray;
  }

  /**
   * Get nested group array using path notation
   * @param groupPath - Path like "type|field" for nested groups
   * @param parentIndex - Index of parent group instance
   */
  getNestedGroupArray(groupPath: string, parentIndex?: number): FormArray {
    const parsed = this.parseGroupPath(groupPath);
    if (parsed.isNested && parsed.parent && parsed.child && parentIndex !== undefined) {
      const parentArray = this.getGroupArray(parsed.parent);
      const parentGroup = parentArray.at(parentIndex) as FormGroup;
      return parentGroup.get(parsed.child) as FormArray;
    }
    return this.getGroupArray(groupPath);
  }

  addGroup(groupName: string, index?: number) {
    const groupArray = this.getGroupArray(groupName);
    const newGroup = this.createGroup(groupName);

    if (index !== undefined) {
      groupArray.insert(index + 1, newGroup);
    } else {
      groupArray.push(newGroup);
    }
  }

  /**
   * Add nested group
   * @param groupPath - Path like "type|field"
   * @param parentIndex - Index of parent group instance
   * @param index - Index to insert at (optional)
   */
  addNestedGroup(groupPath: string, parentIndex: number, index?: number) {
    const parsed = this.parseGroupPath(groupPath);
    if (parsed.isNested && parsed.parent && parsed.child) {
      const nestedArray = this.getNestedGroupArray(groupPath, parentIndex);
      const newGroup = this.createGroup(groupPath);

      if (index !== undefined) {
        nestedArray.insert(index + 1, newGroup);
      } else {
        nestedArray.push(newGroup);
      }
    }
  }

  removeGroup(groupName: string, index: number) {
    this.getGroupArray(groupName).removeAt(index);
  }

  /**
   * Remove nested group
   * @param groupPath - Path like "type|field"
   * @param parentIndex - Index of parent group instance
   * @param index - Index of nested group to remove
   */
  removeNestedGroup(groupPath: string, parentIndex: number, index: number) {
    const nestedArray = this.getNestedGroupArray(groupPath, parentIndex);
    nestedArray.removeAt(index);
  }

  /**
   * Clone nested group
   * @param groupPath - Path like "type|field"
   * @param parentIndex - Index of parent group instance
   * @param index - Index of nested group to clone
   */
  cloneNestedGroup(groupPath: string, parentIndex: number, index: number) {
    const nestedArray = this.getNestedGroupArray(groupPath, parentIndex);
    const groupToClone = nestedArray.at(index) as FormGroup;
    const clonedGroup = this.createGroup(groupPath);

    // Copy values from the original group to the cloned group
    Object.keys(groupToClone.controls).forEach(key => {
      const originalControl = groupToClone.get(key);
      const clonedControl = clonedGroup.get(key);

      if (originalControl && clonedControl) {
        if (originalControl instanceof FormArray) {
          // Handle FormArray cloning
          const originalArray = originalControl as FormArray;
          const clonedArray = clonedControl as FormArray;

          // Clear the default entry and copy all entries from original
          clonedArray.clear();
          originalArray.controls.forEach(control => {
            if (control instanceof FormGroup) {
              const newControl = this.fb.group({});
              Object.keys(control.controls).forEach(subKey => {
                newControl.addControl(subKey, this.fb.control(control.get(subKey)?.value));
              });
              clonedArray.push(newControl);
            }
          });
        } else {
          // Handle regular FormControl cloning
          clonedControl.setValue(originalControl.value);
        }
      }
    });

    nestedArray.insert(index + 1, clonedGroup);
  }



  isFirstFieldInGroup(field: any): boolean {
    return (
      this.fields.findIndex((f) => f.Group === field.Group) ===
      this.fields.indexOf(field)
    );
  }

  /**
   * Check if this is the first field in a parent group (for rendering group headers)
   */
  isFirstFieldInParentGroup(field: any): boolean {
    if (!field.Group) return false;

    const parsed = this.parseGroupPath(field.Group);
    if (!parsed.parent) return false;

    // Find the first field that belongs to this parent group
    const firstFieldIndex = this.fields.findIndex((f) => {
      if (!f.Group) return false;
      const fParsed = this.parseGroupPath(f.Group);
      return fParsed.parent === parsed.parent;
    });

    return firstFieldIndex === this.fields.indexOf(field);
  }

  /**
   * Check if this is the first field in a nested group (for rendering nested group headers)
   */
  isFirstFieldInNestedGroup(field: any): boolean {
    if (!field.Group) return false;

    const parsed = this.parseGroupPath(field.Group);
    if (!parsed.isNested || !parsed.child) return false;

    // Find the first field that belongs to this specific nested group path
    return (
      this.fields.findIndex((f) => f.Group && f.Group.trim() === field.Group.trim()) ===
      this.fields.indexOf(field)
    );
  }

  trackByFieldName(_index: number, field: any): string {
    return field.fieldName;
  }












  // Helper method to extract part before comma for tables API calls
  // Note: extractTablesApiId moved to TableUtilsService

  /**
   * DATA FETCHING: Fetches form data from the backend API
   * Validates ID, constructs API URL, and fetches data for the specified record
   */
  fetchFormData() {
    this.isLoading = true;
    const id = this.form.get("ID")?.value;
    
    // Use screenName if tableName is not available
    const tableNameToUse = this.tableName || this.screenName;
    
    const tablesApiId = this.tableUtilsService.extractTablesApiId(tableNameToUse);
    
    if (!tablesApiId) {
      console.error('❌ Error: No valid table name found for API call');
      this.errorMessage = "No valid table name found";
      this.isLoading = false;
      return;
    }
    
    const apiUrl = `${environment.baseURL}/api/tables/${tablesApiId}/records/${id}`;
    const params = {
      isTenantBased: this.isTenantBasedFlag.toString(),
    };
    this.http.get(apiUrl, { withCredentials: true, params }).subscribe({
      next: (response: any) => {
        if (response && response.data) {
          if(response.data.recordStatus)
          {
            this.isAuth=false;
            this.cdRef.detectChanges();
          }
          this.populateForm(response.data);
        }
        // Handle defaultFields if present in response
        if (response && response.defaultFields && Array.isArray(response.defaultFields)) {
          this.populateDefaultFields(response.defaultFields);
        }
      },
      error: (error) => {
        this.errorMessage = "An error occurred while fetching data";
      },
      complete: () => this.isLoading = false
    });
  }

  // populateForm(data: any) {
  //   Object.keys(data).forEach(key => {
  //     const formControl = this.form.get(key);

  //     if (formControl instanceof FormArray && Array.isArray(data[key])) {
  //       const formArray = formControl as FormArray;
  //       formArray.clear(); // Clear existing controls in the FormArray

  //       if (this.fields.some(field => field.Group === key)) {
  //         // Handle Group fields 
  //         data[key].forEach((groupData: any) => {
  //           formArray.push(this.createGroup(key)); 
  //           (formArray.at(formArray.length - 1) as FormGroup).patchValue(groupData);
  //         });
  //       } else {
  //         // Handle multi-fields 
  //         const field = this.fields.find(field => field.fieldName === key);
  //         if (field) {
  //           // Create form groups in the FormArray
  //           for (let i = 0; i < data[key].length; i++) {
  //             formArray.push(this.createMultiField(field));
  //           }

  //           // Patch the values
  //           data[key].forEach((value: any, index: number) => {
  //             const newGroup = formArray.at(index) as FormGroup;

  //             // Check if the value is an object (for multi-fields with multiple properties)
  //             if (typeof value === 'object' && !Array.isArray(value)) {  
  //               newGroup.patchValue(value); 
  //             } else {
  //               // If the value is not an object, patch it to the field.fieldName
  //               newGroup.patchValue({ [field.fieldName]: value }); 
  //             }
  //           });
  //         }
  //       }
  //     } else if (formControl) {
  //       // For simple fields (not FormArray)
  //       const field = this.fields.find(field => field.fieldName === key);
  //       if (field && field.type === 'date' && typeof data[key] === 'string') {
  //         const parsedDate = new Date(data[key]);
  //         const dateOnly = parsedDate.toISOString().split('T')[0];
  //         if (!isNaN(parsedDate.getTime())) {
  //           formControl.setValue(dateOnly);
  //         } else {
  //           formControl.setValue(null);
  //         }
  //       } else if (field && field.type === 'date' && Array.isArray(data[key])) {
  //         // Handle the case where data[key] is an array of date strings (for multi-fields)
  //         const parsedDates = data[key].map(dateStr => {
  //           const parsedDate = new Date(dateStr);
  //           return !isNaN(parsedDate.getTime()) ? parsedDate : null;
  //         });
  //         formControl.setValue(parsedDates);
  //       } else {
  //         formControl.setValue(data[key]);
  //       }

  //       if (this.isViewMode) {
  //         formControl.disable(); // Disable the control AFTER setting the value
  //       }
  //     }
  //   });
  // }

  /**
   * FORM POPULATION: Populates the form with data from the backend API
   * Handles both flat multi-fields and grouped fields (ledgers)
   */
  populateForm(data: any): void {
    Object.keys(data).forEach(key => {
      const formControl = this.form.get(key);

      if (formControl instanceof FormArray && Array.isArray(data[key])) {
        const formArray = formControl as FormArray;
        formArray.clear();

        if (this.fields.some(field => field.Group === key)) {
          // 🔷 Handle Grouped Fields (e.g., ledger)
          data[key].forEach((groupData: any, groupIndex: number) => {
            const groupForm = this.createGroup(key) as FormGroup;

            // Patch flat fields first (excluding nested arrays)
            const flatGroupData = { ...groupData };
            Object.keys(flatGroupData).forEach(nestedKey => {
              if (Array.isArray(flatGroupData[nestedKey])) {
                delete flatGroupData[nestedKey];
              }
            });
            groupForm.patchValue(flatGroupData);

            // 🔷 Handle nested multi-fields (e.g., denom)
            Object.keys(groupData).forEach(nestedKey => {
              const nestedValue = groupData[nestedKey];
              const nestedControl = groupForm.get(nestedKey);

              if (nestedControl instanceof FormArray && Array.isArray(nestedValue)) {
                nestedControl.clear();

                const nestedFieldMeta = this.fields.find(f =>
                  f.fieldName === nestedKey &&
                  f.Group?.startsWith(key)
                );

                if (nestedFieldMeta) {
                  nestedValue.forEach((item: any, index: number) => {
                    console.log(`📦 Pushing into [${key} > ${nestedKey}] index ${index}:`, item);

                    const nestedGroup = this.createMultiField2(nestedFieldMeta, item); // ← Pass item as second argument
                    nestedGroup.patchValue(item);
                    nestedControl.push(nestedGroup);
                  });
                }
              }
            });

            formArray.push(groupForm);
          });

        } else {
          // 🔶 Handle Flat Multi-Fields
          const field = this.fields.find(field => field.fieldName === key);
          if (field) {
            data[key].forEach((value: any, index: number) => {
              const multiGroup = this.createMultiField(field);
              if (typeof value === 'object' && !Array.isArray(value)) {
                multiGroup.patchValue(value);
              } else {
                multiGroup.patchValue({ [field.fieldName]: value });
              }
              formArray.push(multiGroup);
            });
          }
        }

      } else if (formControl) {
        // For simple fields (not FormArray)
        const field = this.fields.find(field => field.fieldName === key);
        if (field && field.type === 'date' && typeof data[key] === 'string') {
          const parsedDate = new Date(data[key]);
          const dateOnly = parsedDate.toISOString().split('T')[0];
          if (!isNaN(parsedDate.getTime())) {
            formControl.setValue(dateOnly);
          } else {
            formControl.setValue(null);
          }
        } else if (field && field.type === 'date' && Array.isArray(data[key])) {
          // Handle the case where data[key] is an array of date strings (for multi-fields)
          const parsedDates = data[key].map(dateStr => {
            const parsedDate = new Date(dateStr);
            return !isNaN(parsedDate.getTime()) ? parsedDate : null;
          });
          formControl.setValue(parsedDates);
        } else {
          formControl.setValue(data[key]);
        }

        if (this.isViewMode) {
          formControl.disable(); // Disable the control AFTER setting the value
        }
      }
    });
  }
  //populatedefualt fields 
  /**
   * POPULATION: Populates default fields from metadata or API response
   * Handles both simple fields and multi-fields with default values
   */
  populateDefaultFields(defaultFields: any[]) {
    if (!Array.isArray(defaultFields)) return;
    defaultFields.forEach(item => {
      if (item && typeof item === 'object') {
        // Handle API structure: { defaultField, defaultValue }
        if ('defaultField' in item && 'defaultValue' in item) {
          const fieldName = item.defaultField;
          const defaultValue = item.defaultValue;
          const formControl = this.form.get(fieldName);
          if (formControl && defaultValue !== null && defaultValue !== undefined) {
            const wasDisabled = formControl.disabled;
            if (wasDisabled) formControl.enable({ emitEvent: false });
            const field = this.fields.find(f => f.fieldName === fieldName);
            if (field && field.type === 'date' && typeof defaultValue === 'string') {
              const parsedDate = new Date(defaultValue);
              if (!isNaN(parsedDate.getTime())) {
                const dateOnly = parsedDate.toISOString().split('T')[0];
                formControl.setValue(dateOnly);
              }
            } else if (field && field.type === 'boolean') {
              const boolValue = defaultValue === true || defaultValue === 'true' || defaultValue === 1 || defaultValue === '1';
              formControl.setValue(boolValue);
            } else if (field && (field.type === 'int' || field.type === 'double')) {
              const numValue = parseFloat(defaultValue);
              if (!isNaN(numValue)) {
                formControl.setValue(numValue);
              }
            } else {
              formControl.setValue(defaultValue);
            }
            if (wasDisabled) formControl.disable({ emitEvent: false });
          }
        } else {
          // Fallback: handle { fieldName: value } structure
          Object.keys(item).forEach(fieldName => {
            const defaultValue = item[fieldName];
            const formControl = this.form.get(fieldName);
            if (formControl && defaultValue !== null && defaultValue !== undefined) {
              const wasDisabled = formControl.disabled;
              if (wasDisabled) formControl.enable({ emitEvent: false });
              const field = this.fields.find(f => f.fieldName === fieldName);
              if (field && field.type === 'date' && typeof defaultValue === 'string') {
                const parsedDate = new Date(defaultValue);
                if (!isNaN(parsedDate.getTime())) {
                  const dateOnly = parsedDate.toISOString().split('T')[0];
                  formControl.setValue(dateOnly);
                }
              } else if (field && field.type === 'boolean') {
                const boolValue = defaultValue === true || defaultValue === 'true' || defaultValue === 1 || defaultValue === '1';
                formControl.setValue(boolValue);
              } else if (field && (field.type === 'int' || field.type === 'double')) {
                const numValue = parseFloat(defaultValue);
                if (!isNaN(numValue)) {
                  formControl.setValue(numValue);
                }
              } else {
                formControl.setValue(defaultValue);
              }
              if (wasDisabled) formControl.disable({ emitEvent: false });
            }
          });
        }
      }
    });
  }



  /**
   * NAVIGATION: Goes back to the previous record
   * Sends a DELETE request to unlock the record
   */
  goBack() {
    const id = this.form.get("ID")?.value;
    const tableNameToUse = this.tableName || this.screenName;
    const tablesApiId = this.tableUtilsService.extractTablesApiId(tableNameToUse);
    const apiUrl = `${environment.baseURL}/api/tables/${tablesApiId}/records/${id}/force-unlock`;

    this.http.delete(apiUrl, { withCredentials: true }).subscribe({
      next: () => {
        // Record unlocked successfully
      },
      error: (error) => {
        this.cleanupForm();
      },
      complete: () => {
        this.cleanupForm();
      }
    });
  }

  /**
   * FORM CLEANUP: Resets form state and clears all fields
   */
  private cleanupForm() {
    // First remove all controls from the form except ID
    Object.keys(this.form.controls).forEach(key => {
      if (key !== 'ID') {
        this.form.removeControl(key);
      }
    });

    // Reset the form
    this.form.reset();

    // Clear all fields and state
    this.fields = [];
    this.showInitialInput = true;
    this.isViewMode = false;
    this.isAuth = true;
    this.submissionSuccess = false;
    this.validationResult = null;
    this.showValidation = false; // Reset validation state
    this.setFormReadonly(false);


  }

  /**
   * HELPER: Gets all keys of an object
   */
  getKeys(option: any): string[] {
    return Object.keys(option);
  }

  /**
   * TOGGLE: Switches between row view and nested view for grouped fields
   */
  toggleViewMode() {
    this.isRowView = !this.isRowView;
  }

  // Form Actions Component Event Handlers
  onSubmissionSuccess(success: boolean) {
    this.submissionSuccess = success;
  }

  onErrorMessageChange(message: string) {
    this.errorMessage = message;
  }

  onIsLoadingChange(loading: boolean) {
    this.isLoading = loading;
  }

  onShowSuccessPopupChange(show: boolean) {
    this.showSuccessPopup = show;
  }

  onSuccessMessageChange(message: string) {
    this.successMessage = message;
  }

  onValidationResultChange(result: any) {
    this.validationResult = result;
  }

  onGoBackRequested() {
    this.goBack();
  }

  onSetFormReadonly(readonly: boolean) {
    this.setFormReadonly(readonly);
  }

  onPopulateForm(data: any) {
    this.populateForm(data);
  }

  onPopulateDefaultFields(fields: any[]) {
    this.populateDefaultFields(fields);
  }

  onSetViewMode(viewMode: boolean) {
    this.isViewMode = viewMode;
  }

  // Form Header Delegation Methods
  onFormSubmit() {
    this.formActions.onSubmit();
  }

  onFormValidate() {
    this.formActions.validateRecord();
  }

  onFormAuthorize() {
    this.formActions.authorizeRecord();
  }

  onFormReject() {
    this.formActions.onRejectRecord();
  }

  onFormDelete() {
    this.formActions.onDeleteRecord();
  }




  /**
   * GROUP CLONING: Clones a specific group instance
   * Creates a new group and copies values from the original group
   */
  cloneGroup(groupName: string, index: number): void {
    const groupArray = this.getGroupArray(groupName);
    const groupToClone = groupArray.at(index) as FormGroup;

    // Step 1: Add a new empty group using your existing method
    this.addGroup(groupName, index);

    // Step 2: Get the newly inserted group
    const clonedGroup = groupArray.at(index + 1) as FormGroup;

    // Step 3: Copy values (deep clone including nested FormArrays)
    Object.keys(groupToClone.controls).forEach(key => {
      const control = groupToClone.get(key);
      const clonedControl = clonedGroup.get(key);

      if (control instanceof FormArray && clonedControl instanceof FormArray) {
        clonedControl.clear();
        control.controls.forEach(c => {
          const clonedSubGroup = this.fb.group({});
          Object.keys((c as FormGroup).controls).forEach(subKey => {
            clonedSubGroup.addControl(subKey, this.fb.control((c as FormGroup).get(subKey)?.value));
          });
          clonedControl.push(clonedSubGroup);
        });
      } else {
        clonedControl?.setValue(control?.value);
      }
    });
  }



  getInputClass(): string {
    return this.showValidation ? 'invalid-input' : '';
  }

  onValidationChange(showValidation: boolean): void {
    this.showValidation = showValidation;
  }


  isIdValid(): boolean {
    const idValue = this.form.get('ID')?.value;
    return idValue && idValue.trim() !== '';
  }

  // Handle field value changes from child components
  onFieldValueChange(_event: {fieldName: string, value: any}): void {
    // Child component already handles form control update
    // This method can be used for additional logic if needed
  }

  /**
   * Gets the FormArray for a multi-field in SubScreen
   * @param fieldName - Name of the field
   * @param groupIndex - Index of the group (optional)
   * @param groupName - Name of the group (optional)
   * @param nestedGroupIndex - Index of the nested group (optional)
   * @param subScreenId - The SubScreen ID (optional, uses selected if not provided)
   * @returns FormArray for the multi-field
   */
  getSubScreenMultiArray(fieldName: string, groupIndex?: number, groupName?: string, nestedGroupIndex?: number, subScreenId?: string): FormArray {
    const targetForm = subScreenId ? this.getSubScreenForm(subScreenId) : this.getSelectedSubScreenForm();
    if (!targetForm) return this.fb.array([]);

    if (groupIndex !== undefined && groupName) {
      const groupArray = targetForm.get(groupName) as FormArray;
      if (groupArray && groupArray.at(groupIndex)) {
        const group = groupArray.at(groupIndex) as FormGroup;
        if (nestedGroupIndex !== undefined) {
          const nestedGroupArray = group.get(groupName.split('|')[1]) as FormArray;
          if (nestedGroupArray && nestedGroupArray.at(nestedGroupIndex)) {
            const nestedGroup = nestedGroupArray.at(nestedGroupIndex) as FormGroup;
            return nestedGroup.get(fieldName) as FormArray;
          }
        } else {
          return group.get(fieldName) as FormArray;
        }
      }
    } else {
      return targetForm.get(fieldName) as FormArray;
    }

    return this.fb.array([]);
  }

  /**
   * Gets the FormArray for a group in SubScreen
   * @param groupName - Name of the group
   * @param subScreenId - The SubScreen ID (optional, uses selected if not provided)
   * @returns FormArray for the group
   */
  getSubScreenGroupArray(groupName: string, subScreenId?: string): FormArray {
    const targetForm = subScreenId ? this.getSubScreenForm(subScreenId) : this.getSelectedSubScreenForm();
    if (!targetForm) return this.fb.array([]);
    return targetForm.get(groupName) as FormArray;
  }

  /**
   * Gets the FormArray for a nested group in SubScreen
   * @param groupPath - The group path
   * @param subScreenId - The SubScreen ID (optional, uses selected if not provided)
   * @param parentIndex - Index of the parent group
   * @returns FormArray for the nested group
   */
  getSubScreenNestedGroupArray(groupPath: string, subScreenId?: string, parentIndex?: number): FormArray {
    const targetForm = subScreenId ? this.getSubScreenForm(subScreenId) : this.getSelectedSubScreenForm();
    if (!targetForm) return this.fb.array([]);

    const parsed = this.parseGroupPath(groupPath);
    if (parsed.parent && parsed.child) {
      const parentArray = targetForm.get(parsed.parent) as FormArray;
      if (parentArray && parentArray.at(parentIndex || 0)) {
        const parentGroup = parentArray.at(parentIndex || 0) as FormGroup;
        return parentGroup.get(parsed.child) as FormArray;
      }
    }

    return this.fb.array([]);
  }

  /**
   * Adds a multi-field instance in SubScreen
   * @param field - The field configuration
   * @param groupIndex - Index of the group (optional)
   * @param index - Index where to add (optional)
   * @param groupName - Name of the group (optional)
   * @param nestedGroupIndex - Index of the nested group (optional)
   * @param subScreenId - The SubScreen ID (optional, uses selected if not provided)
   */
  addSubScreenMultiField(field: any, groupIndex?: number, index?: number, groupName?: string, nestedGroupIndex?: number, subScreenId?: string) {
    const multiArray = this.getSubScreenMultiArray(field.fieldName, groupIndex, groupName, nestedGroupIndex, subScreenId);
    if (multiArray) {
      const newMultiField = this.createMultiField(field);
      if (index !== undefined) {
        multiArray.insert(index, newMultiField);
      } else {
        multiArray.push(newMultiField);
      }
    }
  }

  /**
   * Removes a multi-field instance in SubScreen
   * @param fieldName - Name of the field
   * @param index - Index to remove
   * @param groupIndex - Index of the group (optional)
   * @param groupName - Name of the group (optional)
   * @param nestedGroupIndex - Index of the nested group (optional)
   * @param subScreenId - The SubScreen ID (optional, uses selected if not provided)
   */
  removeSubScreenMultiField(fieldName: string, index: number, groupIndex?: number, groupName?: string, nestedGroupIndex?: number, subScreenId?: string) {
    const multiArray = this.getSubScreenMultiArray(fieldName, groupIndex, groupName, nestedGroupIndex, subScreenId);
    if (multiArray && multiArray.length > 1) {
      multiArray.removeAt(index);
    }
  }

  /**
   * Adds a group in SubScreen
   * @param groupName - Name of the group to add
   * @param index - Index where to add (optional)
   */
  addSubScreenGroupToForm(groupName: string, index?: number) {
    const groupArray = this.getSubScreenGroupArray(groupName);
    if (groupArray) {
      const newGroup = this.createSubScreenGroup(groupName);
      if (index !== undefined) {
        groupArray.insert(index, newGroup);
      } else {
        groupArray.push(newGroup);
      }
    }
  }

  /**
   * Removes a group in SubScreen
   * @param groupName - Name of the group to remove
   * @param index - Index to remove
   */
  removeSubScreenGroup(groupName: string, index: number) {
    const groupArray = this.getSubScreenGroupArray(groupName);
    if (groupArray && groupArray.length > 1) {
      groupArray.removeAt(index);
    }
  }

  /**
   * Checks if a field is the first field in a group in SubScreen
   * @param field - The field to check
   * @param subScreenId - The SubScreen ID (optional, uses selected if not provided)
   * @returns True if it's the first field in the group
   */
  isFirstFieldInSubScreenGroup(field: any, subScreenId?: string): boolean {
    if (!field.Group) return false;
    
    const targetId = subScreenId || this.getSelectedSubScreenId();
    if (!targetId || !this.lastMetadataResponse) return false;
    
    const subScreenMetadata = this.findSubScreenMetadata(this.lastMetadataResponse, targetId);
    if (!subScreenMetadata || !subScreenMetadata.fieldName) return false;
    
    const groupFields = subScreenMetadata.fieldName.filter((f: any) => f.Group === field.Group);
    return groupFields.length > 0 && groupFields[0].fieldName === field.fieldName;
  }

}


